/*
 * Copyright (c) 2006-2023, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2021-08-20     breo.com     first version
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <rtthread.h>
#include <rtdevice.h>
#include "n32g45x.h"
#include "n32_msp.h"

#ifdef BSP_USING_UART
void n32_msp_usart_init(void *Instance)
{
    GPIO_InitType GPIO_InitCtlStruct;
    USART_Module *USARTx = (USART_Module *)Instance;

    GPIO_InitStruct(&GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Speed = GPIO_Speed_50MHz;
#ifdef BSP_USING_UART1
    if (USART1 == USARTx)
    {
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_USART1, ENABLE);
#ifdef BSP_USING_UART1_PIN_RMP
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP_USART1, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_6;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_7;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_10;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#endif
    }

#endif /* BSP_USING_UART1 */
#ifdef BSP_USING_UART2

    if (USART2 == USARTx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_USART2, ENABLE);
#ifdef BSP_USING_UART2_PIN_RMP1
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP1_USART2, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOD, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_5;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_6;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);

#elif defined (BSP_USING_UART2_PIN_RMP2)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMCP2_USART2, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_8;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);

#elif defined (BSP_USING_UART2_PIN_RMP3)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP3_USART2, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_4;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_5;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);

#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_3;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#endif
    }

#endif /* BSP_USING_UART2 */
#ifdef BSP_USING_UART3

    if (USART3 == USARTx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_USART3, ENABLE);
#if defined(BSP_USING_UART3_PIN_PART_RMP)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_PART_RMP_USART3, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_10;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_11;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#elif defined(BSP_USING_UART3_PIN_ALL_RMP)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_ALL_RMP_USART3, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOD, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_8;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_10;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_11;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#endif
    }

#endif /* BSP_USING_UART3 */
#ifdef BSP_USING_UART4

    if (UART4 == USARTx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_UART4, ENABLE);
#if defined(BSP_USING_UART4_PIN_RMP1)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP1_UART4, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB | RCC_APB2_PERIPH_GPIOE, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_7;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
#elif defined(BSP_USING_UART4_PIN_RMP2)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP2_UART4, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_13;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_14;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#elif defined(BSP_USING_UART4_PIN_RMP3)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP3_UART4, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOD, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_0;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_1;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_10;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_11;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#endif
    }

#endif /* BSP_USING_UART4 */
#ifdef BSP_USING_UART5

    if (UART5 == USARTx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_UART5, ENABLE);
#if defined(BSP_USING_UART5_PIN_RMP1)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP1_UART5, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_13;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_14;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#elif defined(BSP_USING_UART5_PIN_RMP2)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP2_UART5, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOE, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_8;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
#elif defined(BSP_USING_UART5_PIN_RMP3)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP3_UART5, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_8;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC | RCC_APB2_PERIPH_GPIOD, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_12;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
#endif
    }

#endif /* BSP_USING_UART5 */
#ifdef BSP_USING_UART6

    if (UART6 == USARTx)
    {
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_UART6, ENABLE);
#if defined(BSP_USING_UART6_PIN_RMP2)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP2_UART6, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_0;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_1;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#elif defined(BSP_USING_UART6_PIN_RMP3)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP3_UART6, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_0;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_1;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#else /* BSP_USING_UART6_PIN_NORMP */
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOE, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_3;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
#endif
    }

#endif /* BSP_USING_UART6 */
#ifdef BSP_USING_UART7

    if (UART7 == USARTx)
    {
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_UART7, ENABLE);
#if defined(BSP_USING_UART7_PIN_RMP1)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP1_UART7, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_3;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#elif defined(BSP_USING_UART7_PIN_RMP3)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP3_UART7, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOG, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_0;
        GPIO_InitPeripheral(GPIOG, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_1;
        GPIO_InitPeripheral(GPIOG, &GPIO_InitCtlStruct);
#else /* BSP_USING_UART7_PIN_NORMP */
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_4;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_5;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#endif
    }

#endif /* BSP_USING_UART7 */
    /* Add others */
}
#endif /* BSP_USING_SERIAL */

#ifdef BSP_USING_SPI
void n32_msp_deinit(void *Instance)
{
    SPI_Module *SPIx = (SPI_Module *)Instance;
    SPI_Enable(SPIx, DISABLE);
    SPI_I2S_DeInit(SPIx);
    if (SPI1 == SPIx)
    {
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_SPI1, DISABLE);
    }
    else if (SPI2 == SPIx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_SPI2, DISABLE);
    }
    else if (SPI3 == SPIx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_SPI3, DISABLE);
    }
}

void n32_msp_spi_init(void *Instance)
{
    GPIO_InitType GPIO_InitCtlStruct;
    SPI_Module *SPIx = (SPI_Module *)Instance;

    n32_msp_deinit(SPIx);

    GPIO_InitStruct(&GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Speed = GPIO_Speed_50MHz;
#ifdef BSP_USING_SPI1
    if (SPI1 == SPIx)
    {
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_SPI1, ENABLE);
#if   defined (BSP_USING_SPI1_PIN_RMP1)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP1_SPI1, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_15;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_3 | GPIO_PIN_5;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_4;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#elif defined (BSP_USING_SPI1_PIN_RMP2)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP2_SPI1, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_5 | GPIO_PIN_7;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_6;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);

#elif defined (BSP_USING_SPI1_PIN_RMP3)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOE, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP3_SPI1, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_7 | GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_8;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_4;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_5 | GPIO_PIN_7;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_6;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#endif

    }
#endif
#ifdef BSP_USING_SPI2
    if (SPI2 == SPIx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_SPI2, ENABLE);
#if   defined (BSP_USING_SPI1_PIN_RMP1)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP1_SPI2, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_6;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_7 | GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_8;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#elif   defined (BSP_USING_SPI2_PIN_RMP2)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOE, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP2_SPI2, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_10;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_11 | GPIO_PIN_13;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_12;
        GPIO_InitPeripheral(GPIOE, &GPIO_InitCtlStruct);
#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_12;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_13 | GPIO_PIN_15;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_14;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#endif
    }
#endif
#ifdef BSP_USING_SPI3
    if (SPI3 == SPIx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_SPI3, ENABLE);
#if   defined (BSP_USING_SPI3_PIN_RMP1)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP1_SPI3, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOD, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
#endif
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_10 | GPIO_PIN_12;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_11;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#elif   defined (BSP_USING_SPI3_PIN_RMP2)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP2_SPI3, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOD, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_8;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_9 | GPIO_PIN_12;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_11;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStruct);
#elif   defined (BSP_USING_SPI3_PIN_RMP3)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
        GPIO_ConfigPinRemap(GPIO_RMP3_SPI3, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_2;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_3;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.Pin = GPIO_PIN_1;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_0;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#else
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
#if   defined (BSP_USING_SPI_NSS_PIN)
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_15;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#endif
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_3 | GPIO_PIN_5;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_4;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#endif
    }
#endif
    /* Add others */
}
#endif /* BSP_USING_SPI */

#ifdef BSP_USING_SDIO
void n32_msp_sdio_init(void *Instance)
{
    GPIO_InitType GPIO_InitCtlStructure;
    SDIO_Module *SDIOx = (SDIO_Module *)Instance;

    GPIO_InitStruct(&GPIO_InitCtlStructure);
    GPIO_InitCtlStructure.GPIO_Speed = GPIO_Speed_50MHz;

    if (SDIO == SDIOx)
    {
        /* if used dma ... */
        RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_DMA2, ENABLE);

        RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_SDIO, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC | RCC_APB2_PERIPH_GPIOD, ENABLE);
        GPIO_InitCtlStructure.Pin = GPIO_PIN_8 | GPIO_PIN_9 | GPIO_PIN_10 | GPIO_PIN_11 | GPIO_PIN_12;
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStructure);

        GPIO_InitCtlStructure.Pin = GPIO_PIN_2;
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitPeripheral(GPIOD, &GPIO_InitCtlStructure);
    }
}
#endif /* BSP_USING_SDIO */

#ifdef BSP_USING_PWM
void n32_msp_tim_init(void *Instance)
{
    GPIO_InitType GPIO_InitCtlStructure;
    GPIO_InitStruct(&GPIO_InitCtlStructure);
    TIM_Module *TIMx = (TIM_Module *)Instance;

    if (TIMx == TIM1)
    {
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_TIM1, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStructure.Pin = GPIO_PIN_14;
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStructure.GPIO_Speed = GPIO_Speed_50MHz;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStructure);
    }
    
    if (TIMx == TIM2)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM2, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStructure.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_3;
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStructure.GPIO_Speed = GPIO_Speed_50MHz;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStructure);
    }
    if (TIMx == TIM3)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM3, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStructure.GPIO_Speed = GPIO_Speed_50MHz;
        GPIO_InitCtlStructure.Pin = GPIO_PIN_6 ;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStructure);
    }

    if (TIMx == TIM4)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM4, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStructure.GPIO_Speed = GPIO_Speed_50MHz;
        GPIO_InitCtlStructure.Pin = GPIO_PIN_8 | GPIO_PIN_9;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStructure);
    }

    if (TIMx == TIM5)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM5, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStructure.Pin = GPIO_PIN_0;
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStructure.GPIO_Speed = GPIO_Speed_50MHz;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStructure);
    }

    if (TIMx == TIM8)
    {
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_TIM8, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);
        GPIO_InitCtlStructure.Pin = GPIO_PIN_7 | GPIO_PIN_8 | GPIO_PIN_9;
        GPIO_InitCtlStructure.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStructure.GPIO_Speed = GPIO_Speed_50MHz;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStructure);
    }
}
#ifdef PKG_USING_INFRARED
#include "infrared.h"
static void ir_test(int argc, char* argv[])
{
    struct rt_i2c_bus_device * i2c_dev = (struct rt_i2c_bus_device *)rt_device_find("i2c0");
    if (i2c_dev == RT_NULL)
    {
        rt_kprintf("I2C device not found!\n");
        return;
    }
    struct infrared_decoder_data infrared_data = {
        .data.nec.addr = 0xFF,
        .data.nec.key = 0x00,
        .data.nec.repeat = 0,
    };
    if(argc == 2){
        infrared_data.data.nec.key = atoi(argv[1]);
    }
    ir_select_decoder("nec");
    int count = 10;
    while (count--)
    {

        rt_kprintf("APP addr:0x%02X key:0x%02X\n",infrared_data.data.nec.addr,infrared_data.data.nec.key);
        /* 发送数据 */
        infrared_write("nec",&infrared_data);

        rt_thread_mdelay(1000);

    }
    rt_kprintf("Ir send success! \n");

}
MSH_CMD_EXPORT(ir_test, ir_test);
#endif
#endif /* BSP_USING_PWM */

#ifdef BSP_USING_ADC
void n32_msp_adc_init(void *Instance)
{
    GPIO_InitType GPIO_InitCtlStruct;
    GPIO_InitStruct(&GPIO_InitCtlStruct);
    ADC_Module *ADCx = (ADC_Module *)Instance;

#ifdef BSP_USING_ADC1
    if (ADCx == ADC1)
    {
        /* ADC1 & GPIO clock enable */
        RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_ADC1, ENABLE);
        ADC_ConfigClk(ADC_CTRL3_CKMOD_AHB, RCC_ADCHCLK_DIV8);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);

        /* Configure ADC Channel as analog input */
        GPIO_InitCtlStruct.Pin = GPIO_PIN_0 | GPIO_PIN_1 | GPIO_PIN_2 | GPIO_PIN_3;
        GPIO_InitCtlStruct.GPIO_Speed = GPIO_INPUT;
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AIN;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
    }
#endif

#ifdef BSP_USING_ADC2
    if (ADCx == ADC2)
    {
        /* ADC2 & GPIO clock enable */
        RCC_EnableAHBPeriphClk(RCC_AHB_PERIPH_ADC2, ENABLE);
        ADC_ConfigClk(ADC_CTRL3_CKMOD_AHB, RCC_ADCHCLK_DIV8);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOC, ENABLE);

        /* Configure ADC Channel as analog input */
        GPIO_InitCtlStruct.Pin = GPIO_PIN_1;
        GPIO_InitCtlStruct.GPIO_Speed = GPIO_INPUT;
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AIN;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitCtlStruct);
    }
#endif
}
#endif /* BSP_USING_ADC */

#ifdef BSP_USING_HWTIMER
void n32_msp_hwtim_init(void *Instance)
{
    TIM_Module *TIMx = (TIM_Module *)Instance;

#ifdef BSP_USING_HWTIM3
    if (TIMx == TIM3)
    {
        /* TIM3 clock enable */
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM3, ENABLE);
    }
#endif

#ifdef BSP_USING_HWTIM4
    if (TIMx == TIM4)
    {
        /* TIM4 clock enable */
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM4, ENABLE);
    }
#endif

#ifdef BSP_USING_HWTIM5
    if (TIMx == TIM5)
    {
        /* TIM5 clock enable */
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM5, ENABLE);
    }
#endif

#ifdef BSP_USING_HWTIM6
    if (TIMx == TIM6)
    {
        /* TIM6 clock enable */
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM6, ENABLE);
    }
#endif

#ifdef BSP_USING_HWTIM7
    if (TIMx == TIM7)
    {
        /* TIM7 clock enable */
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_TIM7, ENABLE);
    }
#endif
}
#endif

#ifdef BSP_USING_CAN
void n32_msp_can_init(void *Instance)
{
    GPIO_InitType GPIO_InitCtlStruct;
    CAN_Module *CANx = (CAN_Module *)Instance;

    GPIO_InitStruct(&GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Speed = GPIO_Speed_50MHz;
#ifdef BSP_USING_CAN1
    if (CAN1 == CANx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_CAN1, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_12;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);

        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_11;
        GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
    }
#endif
#ifdef BSP_USING_CAN2
    if (CAN2 == CANx)
    {
        RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_CAN2, ENABLE);
        RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_AF_PP;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_13;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
        GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IN_FLOATING;
        GPIO_InitCtlStruct.Pin = GPIO_PIN_12;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
    }
#endif
}
#endif /* BSP_USING_CAN */

void n32_msp_jtag_init(void *Instance)
{
    GPIO_InitType GPIO_InitCtlStruct;
    GPIO_InitStruct(&GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Speed = GPIO_Speed_50MHz;
#if defined(BSP_RMP_SW_JTAG_NO_NJTRST)
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
    GPIO_ConfigPinRemap(GPIO_RMP_SW_JTAG_NO_NJTRST, ENABLE);
    /* Available pin: PB4 */
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_4;
    GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
#elif defined(BSP_RMP_SW_JTAG_SW_ENABLE)
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
    GPIO_ConfigPinRemap(GPIO_RMP_SW_JTAG_SW_ENABLE, ENABLE);
    /* Available pin: PB3, PB4, PA15 */
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_OD;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_3;
    GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_4;
    GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_15;
    GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#elif defined(BSP_RMP_SW_JTAG_DISABLE)
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_AFIO, ENABLE);
    GPIO_ConfigPinRemap(GPIO_RMP_SW_JTAG_DISABLE, ENABLE);
    /* Available pin: PB3, PB4, PA13, PA14, PA15 */
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOA, ENABLE);
    RCC_EnableAPB2PeriphClk(RCC_APB2_PERIPH_GPIOB, ENABLE);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_Out_OD;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_3;
    GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IPU;
    GDPIO_InitCtlStruct.Pin = GPIO_PIN_4;
    GPIO_InitPeripheral(GPIOB, &GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_13;
    GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IPD;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_14;
    GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
    GPIO_InitCtlStruct.GPIO_Mode = GPIO_Mode_IPU;
    GPIO_InitCtlStruct.Pin = GPIO_PIN_15;
    GPIO_InitPeripheral(GPIOA, &GPIO_InitCtlStruct);
#else
    return;
#endif
}


#ifdef RT_USING_FINSH
#include <finsh.h>
#if 0 // defined(BSP_USING_UART2) || defined(BSP_USING_UART3)
static void uart_test_rw(rt_device_t uartx, const char *name)
{
    if (uartx == NULL)
    {
        uartx = rt_device_find(name);
        rt_err_t err = rt_device_open(uartx, RT_DEVICE_FLAG_INT_RX | RT_DEVICE_FLAG_DMA_RX);
        RT_ASSERT(err == RT_EOK);
    }
    rt_device_write(uartx, 0, name, strlen(name));
    rt_device_write(uartx, 0, "\r\n", 2);
    uint8_t recv_buf[64] = {0x0};
    int ret = rt_device_read(uartx, 0, recv_buf, sizeof(recv_buf));
    if (ret != 0)
    {
        for (int i = 0; i < ret; ++i)
            rt_kprintf("[%02x]", recv_buf[i]);
    }
    rt_device_write(uartx, 0, "\r\n", 2);
}
static void uart_test(void)
{
#ifdef BSP_USING_UART2
    static rt_device_t u2 = NULL;
    uart_test_rw(u2, "uart2");
#endif
#ifdef BSP_USING_UART3
    static rt_device_t u3 = NULL;
    uart_test_rw(u3, "uart3");
#endif
#ifdef BSP_USING_UART7
    static rt_device_t u7 = NULL;
    uart_test_rw(u7, "uart7");
#endif
}
MSH_CMD_EXPORT(uart_test, uart_test)
#endif

#if 0 //def RT_USING_I2C

static void i2c_test(void)
{
    struct rt_i2c_bus_device * i2c_dev = (struct rt_i2c_bus_device *)rt_device_find("i2c0");
    if (i2c_dev == RT_NULL)
    {
        rt_kprintf("I2C device not found!\n");
        return;
    }
    rt_uint8_t data[2] = {0xaa, 0x55};
    rt_uint8_t addr = 0x50;
    rt_err_t ret = rt_i2c_master_send(i2c_dev, addr, RT_I2C_IGNORE_NACK, data, 2);
    if (ret < 0)
    {
        rt_kprintf("I2C send failed! ret = %d\n", ret);
        return;
    }
    rt_kprintf("I2C send success! ret = %d\n", ret);

}
MSH_CMD_EXPORT(i2c_test, i2c_test);
#endif

#ifdef RT_USING_I2S// RT_USING_I2S
#define I2S_MASTER        SPI3
#define I2S_MASTER_CLK    RCC_APB1_PERIPH_SPI3
#define I2S_MASTER_PIN_CK GPIO_PIN_3
#define I2S_MASTER_PIN_SD GPIO_PIN_5
#define I2S_MASTER_PIN_WS GPIO_PIN_15

#define I2S_MASTER_DMA            DMA2
#define I2S_MASTER_DMA_CLK        RCC_AHB_PERIPH_DMA2
#define I2S_MASTER_Rx_DMA_Channel DMA2_CH1
#define I2S_MASTER_Rx_DMA_FLAG    DMA2_FLAG_TC1
#define I2S_MASTER_Tx_DMA_Channel DMA2_CH2
#define I2S_MASTER_Tx_DMA_FLAG    DMA2_FLAG_TC2

#define I2S_SLAVE        SPI2
#define I2S_SLAVE_CLK    RCC_APB1_PERIPH_SPI2
#define I2S_SLAVE_PIN_CK GPIO_PIN_12
#define I2S_SLAVE_PIN_SD GPIO_PIN_13
#define I2S_SLAVE_PIN_WS GPIO_PIN_14

#define I2S_SLAVE_DMA            DMA1
#define I2S_SLAVE_DMA_CLK        RCC_AHB_PERIPH_DMA1
#define I2S_SLAVE_Rx_DMA_Channel DMA1_CH4
#define I2S_SLAVE_Rx_DMA_FLAG    DMA1_FLAG_TC4
#define I2S_SLAVE_Tx_DMA_Channel DMA1_CH5
#define I2S_SLAVE_Tx_DMA_FLAG    DMA1_FLAG_TC5

#define I2S_SLAVE_DR_Base  0x4000380C
#define I2S_MASTER_DR_Base 0x40003C0C


#define BufferSize 6
uint16_t I2S_SLAVE_Buffer_Rx[BufferSize] = {0x11, 0x22, 0x33, 0x44, 0x55, 0x66};

void i2s_test(void)
{
    int ret = 0;
    static bool i2s_init = false;
    if (i2s_init == false)
    {   
        i2s_init = true;
        /* Enable I2S_SLAVE DMA clock */
        RCC_EnableAHBPeriphClk(I2S_SLAVE_DMA_CLK, ENABLE);
        // RCC_EnableAHBPeriphClk(I2S_MASTER_DMA_CLK, ENABLE);
        /* SPI2 and SPI3 clocks enable */
        RCC_EnableAPB1PeriphClk(I2S_SLAVE_CLK, ENABLE);
        GPIO_InitType GPIO_InitStructure;
        GPIO_InitStruct(&GPIO_InitStructure);
        /* Configure SPI2 pins: CK, WS and SD ---------------------------------*/
        GPIO_InitStructure.Pin        = GPIO_PIN_12 | GPIO_PIN_13 | GPIO_PIN_15;
        GPIO_InitStructure.GPIO_Speed = GPIO_Speed_50MHz;
        GPIO_InitStructure.GPIO_Mode  = GPIO_Mode_AF_PP;
        GPIO_InitPeripheral(GPIOB, &GPIO_InitStructure);
        // mclk
        GPIO_InitStructure.Pin = GPIO_PIN_6;
        GPIO_InitPeripheral(GPIOC, &GPIO_InitStructure);

        /* Configure SPI3 pins: CK and SD ------------------------------------*/
        // GPIO_InitStructure.Pin = GPIO_PIN_3 | GPIO_PIN_5;
        // GPIO_InitPeripheral(GPIOB, &GPIO_InitStructure);
    
        /* Configure SPI3 pins: WS -------------------------------------------*/
        // GPIO_InitStructure.Pin = GPIO_PIN_15;
        // GPIO_InitPeripheral(GPIOA, &GPIO_InitStructure);


        /* Deinitializes the SPI2 and SPI3 peripheral registers --------------------*/
        SPI_I2S_DeInit(I2S_SLAVE);
        // SPI_I2S_DeInit(I2S_MASTER);
        /* I2S_SLAVE_Rx_DMA_Channel configuration ---------------------------------------------*/
        DMA_InitType DMA_InitStructure;
        DMA_DeInit(I2S_SLAVE_Tx_DMA_Channel);
        DMA_StructInit(&DMA_InitStructure);
        DMA_InitStructure.PeriphAddr     = (uint32_t)I2S_SLAVE_DR_Base;
        DMA_InitStructure.MemAddr        = (uint32_t)I2S_SLAVE_Buffer_Rx;
        DMA_InitStructure.Direction      = DMA_DIR_PERIPH_DST;
        DMA_InitStructure.BufSize        = BufferSize;
        DMA_InitStructure.PeriphInc      = DMA_PERIPH_INC_DISABLE;
        DMA_InitStructure.DMA_MemoryInc  = DMA_MEM_INC_ENABLE;
        DMA_InitStructure.PeriphDataSize = DMA_PERIPH_DATA_SIZE_HALFWORD;
        DMA_InitStructure.MemDataSize    = DMA_MemoryDataSize_HalfWord;
        DMA_InitStructure.CircularMode   = DMA_MODE_NORMAL;
        DMA_InitStructure.Priority       = DMA_PRIORITY_VERY_HIGH;
        DMA_InitStructure.Mem2Mem        = DMA_M2M_DISABLE;
        DMA_Init(I2S_SLAVE_Tx_DMA_Channel, &DMA_InitStructure);

        /* I2S peripheral configuration */
        I2S_InitType I2S_InitStructure;
		I2S_InitStruct(&I2S_InitStructure);
        I2S_InitStructure.Standard       = I2S_STD_PHILLIPS;
        I2S_InitStructure.DataFormat     = I2S_DATA_FMT_16BITS;
        I2S_InitStructure.MCLKEnable     = I2S_MCLK_ENABLE;
        I2S_InitStructure.AudioFrequency = I2S_AUDIO_FREQ_44K;
        I2S_InitStructure.CLKPOL         = I2S_CLKPOL_LOW;
        /* I2S2 configuration */
        I2S_InitStructure.I2sMode = I2S_MODE_MASTER_TX;
        I2S_Init(I2S_SLAVE, &I2S_InitStructure);
        /* Enable I2S_SLAVE Rx request */
        SPI_I2S_EnableDma(I2S_SLAVE, SPI_I2S_DMA_TX, ENABLE);
        /* Enable the I2S2 */
        I2S_Enable(I2S_SLAVE, ENABLE);
        /* Enable DMA1 Channel4 */
        DMA_EnableChannel(I2S_SLAVE_Tx_DMA_Channel, ENABLE);
        while (!DMA_GetFlagStatus(I2S_SLAVE_Tx_DMA_FLAG, DMA1))
        ;
        return ;
    }
    for (size_t i = 0; i < BufferSize; i++)
    {
        I2S_SLAVE_Buffer_Rx[i] = i ;
    }
    DMA_ClearFlag(DMA1_FLAG_GL5, DMA1);
    DMA_EnableChannel(I2S_SLAVE_Tx_DMA_Channel, DISABLE);
    DMA_SetCurrDataCounter(I2S_SLAVE_Tx_DMA_Channel, BufferSize);
    DMA_EnableChannel(I2S_SLAVE_Tx_DMA_Channel, ENABLE);
    rt_kprintf("I2S send success! ret = %d\n", ret);

}
MSH_CMD_EXPORT(i2s_test, i2s_test);
#endif
#ifdef BSP_USING_ADC
#ifdef BSP_USING_ADC1
    #define ADC_DEV_NAME        "adc1"
#else
    #define ADC_DEV_NAME        "adc2"
#endif
#define REFER_VOLTAGE       3300
#define CONVERT_BITS        (1 << 12)
static int adc_vol_sample(int argc, char *argv[])
{
    rt_adc_device_t adc_dev;
    rt_uint32_t value, vol;
    rt_err_t ret = RT_EOK;

    adc_dev = (rt_adc_device_t)rt_device_find(ADC_DEV_NAME);
    if (adc_dev == RT_NULL)
    {
        rt_kprintf("adc sample run failed! can't find %s device!\n", ADC_DEV_NAME);
        return -RT_ERROR;
    }

    for (int i = 1; i <= 18; ++i)
    {
        ret = rt_adc_enable(adc_dev, i);
        value = rt_adc_read(adc_dev, i);
        rt_kprintf("ch=[%d] the value is :[%d] \n", i, value);
        vol = value * REFER_VOLTAGE / CONVERT_BITS;
        rt_kprintf("ch=[%d] the voltage is :[%d] \n", i, vol);
    }

    return ret;
}
MSH_CMD_EXPORT(adc_vol_sample, adc voltage convert sample);
#endif

#if 0 //def BSP_USING_HWTIMER
static rt_err_t timeout_cb(rt_device_t dev, rt_size_t size)
{
    rt_kprintf("this is hwtimer timeout callback fucntion!\n");
    rt_kprintf("timer name is :%s.\n", dev->parent.name);
    rt_kprintf("tick is :%d !\n", rt_tick_get());

    return 0;
}
static int hwtimer_init(const char *name)
{
    rt_err_t ret = RT_EOK;
    rt_hwtimerval_t timeout_s;
    rt_device_t hw_dev = RT_NULL;
    rt_hwtimer_mode_t mode;
    hw_dev = rt_device_find(name);
    if (hw_dev == RT_NULL)
    {
        rt_kprintf("hwtimer sample run failed! can't find %s device!\n", name);
        return -RT_ERROR;
    }
    ret = rt_device_open(hw_dev, RT_DEVICE_OFLAG_RDWR);
    if (ret != RT_EOK)
    {
        rt_kprintf("open %s device failed!\n", name);
        return ret;
    }
    rt_device_set_rx_indicate(hw_dev, timeout_cb);
    mode = HWTIMER_MODE_PERIOD;
    ret = rt_device_control(hw_dev, HWTIMER_CTRL_MODE_SET, &mode);
    if (ret != RT_EOK)
    {
        rt_kprintf("set mode failed! ret is :%d\n", ret);
        return ret;
    }
    timeout_s.sec = 5;
    timeout_s.usec = 0;
    if (rt_device_write(hw_dev, 0, &timeout_s, sizeof(timeout_s)) != sizeof(timeout_s))
    {
        rt_kprintf("set timeout value failed\n");
        return -RT_ERROR;
    }

    rt_thread_mdelay(3500);

    rt_device_read(hw_dev, 0, &timeout_s, sizeof(timeout_s));
    rt_kprintf("Read: Sec = %d, Usec = %d\n", timeout_s.sec, timeout_s.usec);

    return ret;
}

static int hwtimer_sample(int argc, char *argv[])
{
#ifdef BSP_USING_HWTIM6
    hwtimer_init("timer6");
#endif
#ifdef BSP_USING_HWTIM7
    hwtimer_init("timer7");
#endif
    return RT_EOK;
}
MSH_CMD_EXPORT(hwtimer_sample, hwtimer sample);
#endif

typedef void (*pFunction)(void);
/* Internal SRAM config fo MCU */
#define SRAM_BASE_ADDR          (0x20000000)
#define SRAM_SIZE_ALL           (0x24000)
#define SRAM_SIZE_USED          (SRAM_SIZE_ALL - 1024)

/* Constant for BOOT */
#define SRAM_VECTOR_WORD_SIZE   (64)
#define SRAM_VECTOR_ADDR        (SRAM_BASE_ADDR + SRAM_SIZE_ALL- 0x100)
#define BOOT_MARK1_ADDR         (0x1FFFF2D0)    /* BOOT NVIC */
#define BOOT_MARK2_ADDR         (0x1FFFF288)    /* BOOT Code */
#define BOOT_MARK3_ADDR         (0x40024C00)
#define BOOTLOADER_MAGIC_ADDR   (SRAM_BASE_ADDR + SRAM_SIZE_USED)
#define BOOTLOADER_MAGIC_VALUE  0xDEABCDEFU


/**
* @brief  Set system clock as 72M with HSI and PLL.
*/
static void SetSysClock_HSI_PLL(void)
{
    /* It is necessary to initialize the RCC peripheral to the reset state.*/
    RCC_DeInit();
    
    /* Enable HSI   */
    RCC_EnableHsi(ENABLE);
    while (RCC_GetFlagStatus(RCC_FLAG_HSIRD) == RESET)
    {
        /*  If HSI failed to start-up,the clock configuration must be wrong.
            User can add some code here to dela with this problem   */
    }
    
    /* Enable ex mode */
    RCC_EnableAPB1PeriphClk(RCC_APB1_PERIPH_PWR,ENABLE);
    PWR->CTRL3 |= (uint32_t)0x00000001;
    
    /* Enable ICACHE and Prefetch Buffer */
    FLASH_SetLatency(FLASH_LATENCY_2);
    FLASH_PrefetchBufSet(FLASH_PrefetchBuf_EN);
    FLASH_iCacheCmd(FLASH_iCache_EN);
    
    /* AHB prescaler factor set to 1,HCLK = SYSCLK = 72M    */
    RCC_ConfigHclk(RCC_SYSCLK_DIV1);
    /* APB2 prescaler factor set to 1,PCLK2 = HCLK/1 = 72M  */
    RCC_ConfigPclk2(RCC_HCLK_DIV1);
    /* APB1 prescaler factor set to 2,PCLK1 = HCLK/2 = 36M  */
    RCC_ConfigPclk1(RCC_HCLK_DIV2);
    
    /* Config PLL */
    RCC_ConfigPll(RCC_PLL_SRC_HSI_DIV2, RCC_PLL_MUL_18);
    
    /* Enable PLL */
    RCC_EnablePll(ENABLE);
    while (RCC_GetFlagStatus(RCC_FLAG_PLLRD) == RESET)
    {
    }
    
    /* Switch PLL clock to SYSCLK. */
    RCC_ConfigSysclk(RCC_SYSCLK_SRC_PLLCLK);
    while (RCC_GetSysclkSrc() != RCC_CFG_SCLKSTS_PLL)
    {
    }
}

static void jump_to_boot(void)
{

    uint32_t i,*pVec,*pMark;
    uint32_t BootAddr,SPAddr;

    rt_kprintf("start jump to mcu boot!\n");

    /* Disable all interrupt */
    __disable_irq();
    
    /* Config IWDG */
    IWDG_ReloadKey();
    IWDG_WriteConfig(IWDG_WRITE_ENABLE);
    IWDG_SetPrescalerDiv(IWDG_PRESCALER_DIV256);
    
    /* Config MMU */
    pMark = (uint32_t *)(BOOT_MARK3_ADDR);
    *pMark = (uint32_t)0x00000011;
    
    /* Config system clock as 72M with HSI and PLL  */
    SetSysClock_HSI_PLL();
    
    /* Reset peripheral used by boot */
    USART_DeInit(USART1);
    GPIO_DeInit(GPIOA);
    RCC_EnableAPB1PeriphReset(RCC_APB1_PERIPH_USB, ENABLE);
    RCC_EnableAPB1PeriphReset(RCC_APB1_PERIPH_USB, DISABLE);
    
    /* Init vector */
    pVec = (uint32_t *)SRAM_VECTOR_ADDR;
    for(i=0;i<SRAM_VECTOR_WORD_SIZE;i++)
        pVec[i] = 0;
    
    /* Get SP addr */
    SPAddr = (*((uint32_t *)BOOT_MARK2_ADDR));
    
    /* Get usefull fuction addr */
    pMark = (uint32_t *)BOOT_MARK1_ADDR;
    if(*pMark != 0xFFFFFFFF)    /*BOOT V2.3 and above*/
    {
        BootAddr                        = pMark[0];
        pVec[SysTick_IRQn+16]           = pMark[1];
        pVec[USART1_IRQn+16]            = pMark[2];
        pVec[USB_LP_CAN1_RX0_IRQn+16]   = pMark[3];
        pVec[RTC_IRQn+16]               = pMark[4];
    }
    else
    {
        if(SPAddr != 0xFFFFFFFF)    /*BOOT V2.2*/
        {
            pVec[SysTick_IRQn+16]           = 0x1FFF0A67;
            pVec[USART1_IRQn+16]            = 0x1FFF0A9F;
            pVec[USB_LP_CAN1_RX0_IRQn+16]   = 0x1FFF0ACF;
            pVec[RTC_IRQn+16]               = 0x1FFF0AD3;
            BootAddr                        = 0x1FFF00D9;
        }
        else    /*BOOT V2.1*/
        {
            pVec[SysTick_IRQn+16]           = 0x1FFF10D7;
            pVec[USART1_IRQn+16]            = 0x1FFF115D;
            pVec[USB_LP_CAN1_RX0_IRQn+16]   = 0x1FFF117F;
            pVec[RTC_IRQn+16]               = 0x1FFF1183;
            pVec[EXTI15_10_IRQn+16]         = 0x1FFF10ED;
            BootAddr                        = 0x1FFF0101;
            SPAddr                          = 0x20008690;
        }
    }
    
    /* Enable interrupt */
    __enable_irq();
    
    /* Set JumpBoot addr */
    pFunction JumpBoot = (pFunction)BootAddr;
    
    /* Initalize Stack Pointer */
    __set_MSP(SPAddr);
    
    /* Initialize vector table */
    SCB->VTOR = SRAM_VECTOR_ADDR;
    /* Jump to BOOT */
    JumpBoot();

}
void mcu_boot_process(void)
{
    uint32_t pMark = 0;
    /* Enable write access to Backup domain */
    PWR_BackupAccessEnable(ENABLE);
    /* Disable Tamper pin */
    BKP_TPEnable(DISABLE);
    /* Disable Tamper interrupt */
    BKP_TPIntEnable(DISABLE);
    /* Clear Tamper pin Event(TE) pending flag */
    BKP_ClrTEFlag();
    pMark = BKP_ReadBkpData(BKP_DAT1);
    pMark |= (uint32_t)BKP_ReadBkpData(BKP_DAT2) << 16;
    rt_kprintf("\nbootloader magic %08X!\n", pMark);
    if(pMark == (uint32_t)BOOTLOADER_MAGIC_VALUE){
        BKP_WriteBkpData(BKP_DAT1, 0);
        BKP_WriteBkpData(BKP_DAT2, 0);
        jump_to_boot();
    }
}
static void mcu_boot(void)
{
    uint32_t pMark = 0;
    rt_kprintf("set jump to mcu boot!\n");
    pMark = (uint32_t)BOOTLOADER_MAGIC_VALUE;
    BKP_WriteBkpData(BKP_DAT1, (uint16_t)(pMark & 0x0000FFFF));
    BKP_WriteBkpData(BKP_DAT2, (uint16_t)(pMark >> 16));
    rt_hw_cpu_reset();
}
MSH_CMD_EXPORT(mcu_boot, jump to mcu internal boot);

#endif
