/*
 * Copyright (c) 2006-2018, RT-Thread Development Team
 *
 * SPDX-License-Identifier: Apache-2.0
 *
 * Change Logs:
 * Date           Author       Notes
 * 2018-01-30     armink       the first version
 * 2018-08-27     Murphy       update log
 */

#include <rtthread.h>
#include <stdlib.h>
#include <stdio.h>
#include <stdbool.h>
#include <finsh.h>
#include <fal.h>
#include <ymodem.h>

#define DBG_ENABLE
#define DBG_SECTION_NAME               "ymodem"
#ifdef OTA_DOWNLOADER_DEBUG
#define DBG_LEVEL                      DBG_LOG
#else
#define DBG_LEVEL                      DBG_INFO
#endif
#define DBG_COLOR
#include <rtdbg.h>

#ifdef PKG_USING_YMODEM_OTA

#define DEFAULT_DOWNLOAD_PART "download"

static size_t update_file_total_size, update_file_cur_size;
static const struct fal_partition * dl_part = RT_NULL;
static uint8_t enable_output_log = 0;
static uint32_t crc32 =0 ;

static enum rym_code ymodem_on_begin(struct rym_ctx *ctx, rt_uint8_t *buf, rt_size_t len)
{
    char *file_name, *file_size;

    /* calculate and store file size */
    file_name = (char *)&buf[0];
    // 查找CRC32值 (最后一个-后面的8位十六进制)
    const char* crc_start = strrchr(file_name, '-');
    if (!crc_start || strlen(crc_start) < 9) { // "-" + 8位十六进制
        return RYM_CODE_CAN;
    }
    crc_start += 1; // 跳过 "-"

    // 提取版本号
    // size_t version_len = crc_start - version_start - 1; // -1 for the '-' before CRC
    // if (version_len >= sizeof(file_info->version)) {
    //     version_len = sizeof(file_info->version) - 1;
    // }
    // strncpy(file_info->version, version_start, version_len);
    // file_info->version[version_len] = '\0';

    // 提取CRC32值
    char crc_str[9];
    strncpy(crc_str, crc_start, 8);
    crc_str[8] = '\0';

    // 检查是否都是十六进制字符
    for (int i = 0; i < 8; i++) {
        if (!((crc_str[i] >= '0' && crc_str[i] <= '9') ||
              (crc_str[i] >= 'a' && crc_str[i] <= 'f') ||
              (crc_str[i] >= 'A' && crc_str[i] <= 'F'))) {
            return RYM_CODE_CAN;
        }
    }

    crc32 = strtoul(crc_str, NULL, 16);

    file_size = (char *)&buf[rt_strlen(file_name) + 1];
    update_file_total_size = atol(file_size);
    if (enable_output_log) {rt_kprintf("Ymodem file_size:%d\n", update_file_total_size);}

    update_file_cur_size = 0;

    /* Get download partition information and erase download partition data */
    if (update_file_total_size > dl_part->len)
    {
        if (enable_output_log) {LOG_E("Firmware is too large! File size (%d), '%s' partition size (%d)", update_file_total_size, dl_part->name, dl_part->len);}
        return RYM_CODE_CAN;
    }

    if (enable_output_log) {LOG_I("Start erase. Size (%d)", update_file_total_size);}

    /* erase DL section */
    if (fal_partition_erase(dl_part, 0, update_file_total_size) < 0)
    {
        if (enable_output_log) {LOG_E("Firmware download failed! Partition (%s) erase error!", dl_part->name);}
        return RYM_CODE_CAN;
    }

    return RYM_CODE_ACK;
}

static enum rym_code ymodem_on_data(struct rym_ctx *ctx, rt_uint8_t *buf, rt_size_t len)
{
    /* write data of application to DL partition  */
    if (fal_partition_write(dl_part, update_file_cur_size, buf, len) < 0)
    {
        if (enable_output_log) {LOG_E("Firmware download failed! Partition (%s) write data error!", dl_part->name);}
        return RYM_CODE_CAN;
    }

    update_file_cur_size += len;

    return RYM_CODE_ACK;
}

#define BOOT_INFO_MAGIC   0xA5A5A5A5  // 魔术字，用于判断该区域是否有效
#define BOOT_INFO_ADDR    (FLASH_BASE + 0x80000 - 0x800)  /**< 启动信息地址 */

typedef enum {
    APP_TARGET_APP1 = 0x01,
    APP_TARGET_APP2 = 0x02
} LDS_APP_TARGET_E;
 
typedef struct {
    uint32_t magic;           // 魔术字，用于校验
    LDS_APP_TARGET_E boot_target;  // 启动目标：APP1 或 APP2
    uint32_t app1_crc32;        // APP1 固件的 CRC32 校验值
    uint32_t app1_size;         // APP1 固件的大小
    char app1_version[8];      // APP1 版本号
    uint32_t app2_crc32;        // APP2 固件的 CRC32 校验值
    uint32_t app2_size;       // APP2 固件的大小
    char app2_version[8];      // APP2 版本号
    uint32_t crc32;           // boot 信息检验值
} BootInfo_t;
extern uint32_t ldsUtilCheckCrc32(const uint8_t *data, uint32_t len);
void bootInfoUpdate(void)
{
    // 读取启动信息
    BootInfo_t *boot_info = (BootInfo_t*)BOOT_INFO_ADDR;
    BootInfo_t new_boot = *boot_info;
    if (boot_info->magic == BOOT_INFO_MAGIC) {
        uint32_t boot_info_crc = ldsUtilCheckCrc32((const uint8_t*)boot_info, (sizeof(BootInfo_t) - 4));
        if (boot_info_crc == boot_info->crc32){
            rt_kprintf("Boot info is valid.\n");
            rt_kprintf("Boot target: %s\n", boot_info->boot_target == APP_TARGET_APP1 ? "APP1" : "APP2");
            rt_kprintf("APP1 info: version %s, size %d, crc32 0x%08x\n", boot_info->app1_version, boot_info->app1_size, boot_info->app1_crc32);
            rt_kprintf("APP2 info: version %s, size %d, crc32 0x%08x\n", boot_info->app2_version, boot_info->app2_size, boot_info->app2_crc32);
        }
    }
    rt_memset(&new_boot, 0, sizeof(BootInfo_t));
    new_boot.magic = BOOT_INFO_MAGIC;
    new_boot.boot_target = APP_TARGET_APP2;
    new_boot.app1_crc32 = crc32;
    new_boot.app1_size = update_file_total_size;
    strncpy(new_boot.app1_version, "1.01.00", sizeof(new_boot.app1_version) - 1);
    new_boot.app1_version[sizeof(new_boot.app1_version) - 1] = 0;

    // 计算并更新boot info的CRC32
    new_boot.crc32 = ldsUtilCheckCrc32((const uint8_t*)&new_boot, (sizeof(BootInfo_t) - 4));
    printf("New boot info: target: %s, app2_crc32: 0x%08lX, app2_size: %lu, app2_version: %s, crc32: 0x%08lX\n",
            (new_boot.boot_target == APP_TARGET_APP1) ? "APP1" : "APP2",
            new_boot.app2_crc32, new_boot.app2_size, new_boot.app2_version, new_boot.crc32);
    fal_partition_erase(fal_partition_find("bootInfo"), 0, 2048);
    fal_partition_write(fal_partition_find("bootInfo"), 0, (const uint8_t*)&new_boot, sizeof(BootInfo_t));
}
void ymodem_ota(uint8_t argc, char **argv)
{
    struct rym_ctx rctx;
    const char str_usage[] = "Usage: ymodem_ota -p <partiton name> -t <device name>.\n";
    int i;
    char* recv_partition = DEFAULT_DOWNLOAD_PART;
    rt_device_t dev = rt_console_get_device();
    enable_output_log = 0;
    
    for (i=1; i<argc;)
    {
        /* change default partition to save firmware */
        if (!strcmp(argv[i], "-p"))
        {
            if (argc <= (i+1))
            {
                rt_kprintf("%s", str_usage);
                return;
            }
            recv_partition = argv[i+1];
            i += 2;
        }
        /* change default device to transfer */
        else if (!strcmp(argv[i], "-t"))
        {
            if (argc <= (i+1))
            {
                rt_kprintf("%s", str_usage);
                return;
            }
            dev = rt_device_find(argv[i+1]);
            if (dev == RT_NULL)
            {
                rt_kprintf("Device (%s) find error!\n", argv[i+1]);
                return;
            }
            i += 2;
        }
        /* NOT supply parameter */
        else
        {
            rt_kprintf("%s", str_usage);
            return;
        }
    }
    if ((dl_part = fal_partition_find(recv_partition)) == RT_NULL)
    {
        rt_kprintf("Partition (%s) find error!\n", recv_partition);
        return;
    }
    if (dev != rt_console_get_device()) {enable_output_log = 1;}
    rt_kprintf("Save firmware on \"%s\" partition with device \"%s\".\n", recv_partition, dev->parent.name);
    rt_kprintf("Warning: Ymodem has started! This operator will not recovery.\n");
    rt_kprintf("Please select the ota firmware file and use Ymodem to send.\n");

    if (!rym_recv_on_device(&rctx, dev, RT_DEVICE_OFLAG_RDWR | RT_DEVICE_FLAG_INT_RX,
                            ymodem_on_begin, ymodem_on_data, NULL, RT_TICK_PER_SECOND))
    {
        rt_kprintf("Download firmware to flash success.\n");
        rt_kprintf("System now will restart...\r\n");

        /* wait some time for terminal response finish */
        rt_thread_delay(rt_tick_from_millisecond(200));

        /* Reset the device, Start new firmware */
        extern void rt_hw_cpu_reset(void);
        rt_hw_cpu_reset();
        /* wait some time for terminal response finish */
        rt_thread_delay(rt_tick_from_millisecond(200));
    }
    else
    {
        /* wait some time for terminal response finish */
        rt_thread_delay(RT_TICK_PER_SECOND);
        rt_kprintf("Update firmware fail.\n");
    }

    return;
}
/**
 * msh />ymodem_ota
*/
MSH_CMD_EXPORT(ymodem_ota, Use Y-MODEM to download the firmware);

#endif /* PKG_USING_YMODEM_OTA */
