#if 0
#include <stdint.h>
#include "lds_boot.h"
#include "n32g45x.h"

#define BOOT_INFO_MAGIC   0xA5A5A5A5  // 魔术字，用于判断该区域是否有效

typedef enum {
    APP_TARGET_APP1 = 0x01,
    APP_TARGET_APP2 = 0x02
} LDS_APP_TARGET_E;
 
typedef struct {
    uint32_t magic;           // 魔术字，用于校验
    LDS_APP_TARGET_E boot_target;  // 启动目标：APP1 或 APP2
    uint32_t app1_crc32;        // APP1 固件的 CRC32 校验值
    uint32_t app1_size;         // APP1 固件的大小
    char app1_version[8];      // APP1 版本号
    uint32_t app2_crc32;        // APP2 固件的 CRC32 校验值
    uint32_t app2_size;       // APP2 固件的大小
    char app2_version[8];      // APP2 版本号
    uint32_t crc32;           // boot 信息检验值
} BootInfo_t;


#define VECTOR_TABLE_SIZE  (98) // 假设 MCU 有 114 个向量
__attribute__((aligned(256))) static uint32_t ram_vector_table[VECTOR_TABLE_SIZE];
 

/*
 * @brief  获取当前代码的实际运行基地址
 * @note   通过读取 PC 指针的值并进行对齐来确定
 * @retval 当前代码运行的基地址
 */
uint32_t get_running_base_address(void) {
    uint32_t pc;
    // 使用内联汇编获取当前 PC 值
    __asm__ volatile("mov %0, pc" : "=r"(pc));
    // 将 PC 值向下对齐到 Flash 分区的边界（例如 256KB 边界）
    // 0x08008000 -> 0x08000000; 0x08040000 -> 0x08040000
    // 这里使用一个简单的掩码，需要根据你的分区大小调整
    return (pc & 0xFFFC0000); // 假设分区大小是 256K (0x40000)
}
 
 
/*
 * @brief  重定向中断向量表
 * @note   此函数应在 SystemInit 中被调用
 */
void remap_vector_table(void) {
    // 获取当前实际运行的基地址 (e.g., 0x08008000 or 0x08040000)
    uint32_t running_base = get_running_base_address();
    
    // 如果已经在 RAM 中运行，则不执行重映射（可选的健壮性检查）
    if (running_base < 0x20000000) { 
        // 获取 Flash 中的原始向量表 (模板)
        uint32_t *flash_vector_table = (uint32_t *)running_base;
 
        // 计算地址偏移量
        int32_t offset = running_base - COMPILED_BASE_ADDRESS;
 
        // 复制并修正向量表到 RAM
        for (int i = 0; i < VECTOR_TABLE_SIZE; i++) {
            // 第一个字是栈顶指针，通常不需要修改，直接复制
            if (i == 0) {
                ram_vector_table[i] = flash_vector_table[i];
            } else {
                // 其他都是函数地址，需要加上偏移量
                // 注意：最低位（Thumb状态位）必须保留
                uint32_t original_handler = flash_vector_table[i];
                if (original_handler != 0) { // 检查是否是未使用的向量
                    ram_vector_table[i] = (original_handler + offset);
                } else {
                    ram_vector_table[i] = 0;
                }
            }
        }
 
        // 将 VTOR 指向 RAM 中的新向量表
        SCB->VTOR = (uint32_t)ram_vector_table & NVIC_VTOR_MASK;
    }
}
#endif