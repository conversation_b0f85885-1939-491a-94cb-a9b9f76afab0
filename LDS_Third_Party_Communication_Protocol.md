# LDS 第三方通讯协议文档

## 协议概述

LDS 第三方通讯协议是一个基于 UART 的通讯协议，用于与第三方设备进行通信。协议采用大端字节序（MSB first），支持命令队列、重传机制和 CRC16-modbus 校验。

## 协议格式

### 帧结构
```
Head(2) + CMD(1) + SEQ(1) + LEN(2) + DATA(variable) + CRC16(2)
```

| 字段 | 长度(字节) | 描述 | 值 |
|------|-----------|------|-----|
| Head | 2 | 帧头 | 固定为 0x5AA5 |
| CMD | 1 | 命令码 | 见命令定义 |
| SEQ | 1 | 序列号 | 0x00-0xFF，用于请求/响应匹配 |
| LEN | 2 | 数据长度 | DATA 字段的字节数（大端） |
| DATA | 可变 | 数据载荷 | 最大 120 字节 |
| CRC16 | 2 | 校验码 | 从 Head 到 CRC16-1 的 CRC16 校验（大端） |

### 协议常量
- 帧头1: `0x5A`
- 帧头2: `0xA5`
- 最小帧长: 8 字节（无数据）
- 最大帧长: 128 字节
- 最大数据长度: 120 字节
- 响应标志: `0x80`（响应帧的 CMD 字段会加上此标志）

## 命令定义

| 命令码 | 命令名称 | 描述 |
|--------|----------|------|
| 0x01 | MUTE | 静音控制命令 |
| 0x02 | EFFECT | 音效模式命令 |
| 0x03 | POWER_CTRL | 电源控制命令 |
| 0x04 | VERSION | 版本查询命令 |
| 0x05 | STATUS | 状态查询命令 |

## 命令详细说明

### 1. 静音控制命令 (0x01)

#### 请求格式
```
发送: 5A A5 01 [SEQ] 00 02 [MAIN] [MUTE] [CRC16_H] [CRC16_L]
```

**数据字段:**
- `MAIN`: 设备选择
  - `0x00`: 主设备
  - `0x01`: 从设备
- `MUTE`: 静音状态
  - `0x00`: 取消静音
  - `0x01`: 静音

#### 响应格式
```
接收: 5A A5 81 [SEQ] 00 03 [MAIN] [MUTE] [ACK] [CRC16_H] [CRC16_L]
```

**响应数据:**
- `MAIN`: 回显设备选择
- `MUTE`: 回显静音状态
- `ACK`: 执行结果（0=成功，非0=失败）

#### 示例
**静音主设备:**
```
发送: 5A A5 01 01 00 02 00 01 [CRC16]
接收: 5A A5 81 01 00 03 00 01 00 [CRC16]
```

**取消静音从设备:**
```
发送: 5A A5 01 02 00 02 01 00 [CRC16]
接收: 5A A5 81 02 00 03 01 00 00 [CRC16]
```

### 2. 音效模式命令 (0x02)

#### 请求格式
```
发送: 5A A5 02 [SEQ] 00 01 [EFFECT] [CRC16_H] [CRC16_L]
```

**数据字段:**
- `EFFECT`: 音效模式
  - `0x00`: 标准模式
  - `0x01`: 女声模式
  - `0x02`: 男声模式

#### 响应格式
```
接收: 5A A5 82 [SEQ] 00 02 [EFFECT] [ACK] [CRC16_H] [CRC16_L]
```

#### 示例
**设置女声模式:**
```
发送: 5A A5 02 03 00 01 01 [CRC16]
接收: 5A A5 82 03 00 02 01 00 [CRC16]
```

**设置男声模式:**
```
发送: 5A A5 02 04 00 01 02 [CRC16]
接收: 5A A5 82 04 00 02 02 00 [CRC16]
```

### 3. 电源控制命令 (0x03)

#### 请求格式
```
发送: 5A A5 03 [SEQ] 00 02 [DEVICE] [CTRL] [CRC16_H] [CRC16_L]
```

**数据字段:**
- `DEVICE`: 设备选择
  - `0x00`: 麦克风设备
  - `0x01`: 智能底座设备
- `CTRL`: 控制命令（具体值待定义）

#### 响应格式
```
接收: 5A A5 83 [SEQ] 00 03 [DEVICE] [CTRL] [ACK] [CRC16_H] [CRC16_L]
```

#### 示例
**控制麦克风设备电源:**
```
发送: 5A A5 03 05 00 02 00 01 [CRC16]
接收: 5A A5 83 05 00 03 00 01 00 [CRC16]
```

### 4. 版本查询命令 (0x04)

#### 请求格式
```
发送: 5A A5 04 [SEQ] 00 01 [DEVICE] [CRC16_H] [CRC16_L]
```

**数据字段:**
- `DEVICE`: 设备选择
  - `0x00`: 麦克风设备
  - `0x01`: 智能底座设备

#### 响应格式
```
接收: 5A A5 84 [SEQ] [LEN_H] [LEN_L] [DEVICE] [VERSION_STRING...] [ACK] [CRC16_H] [CRC16_L]
```

**响应数据:**
- `DEVICE`: 回显设备选择
- `VERSION_STRING`: 版本字符串（ASCII）
- `ACK`: 执行结果

#### 示例
**查询麦克风版本:**
```
发送: 5A A5 04 06 00 01 00 [CRC16]
接收: 5A A5 84 06 00 0A 00 76 31 2E 30 2E 30 00 [CRC16]
       (版本字符串: "v1.0.0")
```

### 5. 状态查询命令 (0x05)

#### 请求格式
```
发送: 5A A5 05 [SEQ] 00 00 [CRC16_H] [CRC16_L]
```

#### 响应格式
```
接收: 5A A5 85 [SEQ] 00 05 [MIC_STATUS] [BASE_STATUS] [MIC_POWER] [BASE_POWER] [ACK] [CRC16_H] [CRC16_L]
```

**响应数据:**
- `MIC_STATUS`: 麦克风状态
- `BASE_STATUS`: 智能底座状态
- `MIC_POWER`: 麦克风电源状态
- `BASE_POWER`: 智能底座电源状态
- `ACK`: 执行结果

#### 示例
**查询系统状态:**
```
发送: 5A A5 05 07 00 00 [CRC16]
接收: 5A A5 85 07 00 05 01 01 01 01 00 [CRC16]
```

## 协议特性

### 1. 序列号机制
- 每个请求帧都有唯一的序列号（0x00-0xFF）
- 响应帧使用相同的序列号进行匹配
- 序列号自动递增，溢出后从 0 开始

### 2. 重传机制
- 支持命令队列，最多 16 个待处理命令
- 响应超时时间：2 秒
- 最大重试次数：2 次
- 连续错误超过 10 次认为设备未连接

### 3. CRC16 校验
- 使用 CRC16-modbus 计算, 多项式为0x8005，输入、输出反转，初始值为0xFFFF,结果异或值为0x0000
- 校验范围：从帧头到 CRC16 字段之前的所有字节
- 大端字节序传输

### 4. 状态机解析
- 支持字节流解析
- 解析超时：1 秒
- 自动错误恢复

## 错误处理

### 1. CRC 校验错误
- 丢弃错误帧
- 重置状态机
- 记录错误日志

### 2. 帧格式错误
- 数据长度超限
- 无效命令码
- 状态机超时

### 3. 通信错误
- 设备未初始化
- UART 写入失败
- 命令队列满

## 使用示例

### 初始化
```c
int ret = ldsThirdPartyInit();
```

### 发送命令
```c
uint8_t data[2] = {0x00, 0x01}; // 主设备静音
int ret = ldsThirdPartySendCommand(LDS_THIRD_PARTY_CMD_MUTE, data, 2);
```

### 清理
```c
int ret = ldsThirdPartyDeinit();
```
