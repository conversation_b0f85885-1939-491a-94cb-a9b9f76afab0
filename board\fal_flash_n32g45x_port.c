/**
 * @file fal_flash_n32g45x_port.c
 * <AUTHOR> name (<EMAIL>)
 * @brief 
 * @version 0.1
 * @date 2025-05-30
 * 
 * @copyright Copyright (c) 2025
 * 
 */

#include <fal.h>

#include "board.h"

#define DBG_TAG "flash"
// #define DBG_LVL DBG_INFO
#include <rtdbg.h>

extern void ldsWdtFeed(void);


static int init(void)
{
    /* do nothing now */
    return 0;
}

static int read(long offset, rt_uint8_t *buf, rt_size_t size)
{
    rt_size_t i;
    rt_uint32_t addr = n32g45x_onchip_flash.addr + offset;

    if ((addr + size) > N32_FLASH_END_ADDRESS)
    {
        LOG_E("read outrange flash size! addr is (0x%p)", (void *)(addr + size));
        return -RT_EINVAL;
    }

    for (i = 0; i < size; i++, addr++, buf++)
    {
        *buf = *(rt_uint8_t *) addr;
    }

    return size;
}

static int write(long offset, const rt_uint8_t *buf, rt_size_t size)
{
    rt_size_t i;
    rt_uint32_t read_data;
    rt_uint32_t write_data;
    rt_uint32_t addr = n32g45x_onchip_flash.addr + offset;

    if (addr % 4 != 0)
    {
        LOG_E("write addr %08x must be 4-byte alignment", addr);
        return -RT_EINVAL;
    }

    if ((addr + size) > N32_FLASH_END_ADDRESS)
    {
        LOG_E("write outrange flash size! addr is (0x%p)", (void *)(addr + size));
        return -RT_EINVAL;
    }

    FLASH_Unlock();
    FLASH_ClearFlag(FLASH_STS_CLRFLAG | FLASH_FLAG_OBERR);

    for (i = 0; i < size; )
    {
        /* write data */
        write_data = 0;
        rt_memcpy(&write_data, buf, size - i >= 4 ? 4 : size - i);
        if(FLASH_ProgramWord(addr, write_data) != FLASH_COMPL)
        {
            FLASH_Lock();
            LOG_E("write not complete, stop at %08X\n", addr);
            return -1;
        }
        read_data = *(rt_uint32_t *) addr;
        /* check data */
        if (read_data != write_data)
        {
            FLASH_Lock();
            LOG_E("write error at offset 0x%08X, expected: 0x%08X, read: 0x%08X, size %u\n", offset + i, write_data, read_data, size);
            return -1;
        }
        i += 4;
        buf += 4;
        addr += 4;
    }
    FLASH_Lock();

    return size;
}

static int erase(long offset, rt_size_t size)
{
    FLASH_STS flash_status;
    rt_uint32_t cur_erase_sector;
    rt_uint32_t addr = n32g45x_onchip_flash.addr + offset;
    rt_size_t end_addr = addr + size;
    
    if (end_addr > N32_FLASH_END_ADDRESS)
    {
        LOG_E("erase outrange flash size! addr is (0x%p)", (void *)(end_addr));
        return -RT_EINVAL;
    }
    /* start erase */
    FLASH_Unlock();
    FLASH_ClearFlag(FLASH_STS_CLRFLAG | FLASH_FLAG_OBERR);
    /* it will stop when erased size is greater than setting size */
    while (addr < end_addr)
    {
        cur_erase_sector = RT_ALIGN_DOWN(addr, FLASH_PAGE_SIZE);
        flash_status = FLASH_EraseOnePage(cur_erase_sector);
        if (flash_status != FLASH_COMPL)
        {
            FLASH_Lock();
            LOG_E("erase not complete, stop at %08X, ret %d\n", cur_erase_sector, flash_status);
            return -1;
        }
        addr += FLASH_PAGE_SIZE;
        ldsWdtFeed();
    }
    FLASH_Lock();

    return size;
}

const struct fal_flash_dev n32g45x_onchip_flash =
{
    .name       = NOR_FLASH_DEV_NAME,
    .addr       = N32_FLASH_START_ADRESS,
    .len        = N32_FLASH_SIZE,
    .blk_size   = FLASH_PAGE_SIZE,
    .ops        = {init, read, write, erase},
    .write_gran = 32
};

